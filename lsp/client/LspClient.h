#ifndef LSP_CLIENT_LSPCLIENT_H
#define LSP_CLIENT_LSPCLIENT_H

#include <QObject>
#include <QProcess>
#include <QJsonObject>
#include <QJsonDocument>
#include <QByteArray>
#include <QString>
#include <QMap>
#include <QTimer>
#include <QMutex>
#include <memory>
#include "../factory/LspMessageFactory.h"
#include "../factory/LspMessageRegistry.h"
#include "../types/LspTypes.h"
#include "../messages/LspMessages.h"

/**
 * @class LspClient
 * @brief A Qt class to manage and communicate with a Language Server Protocol (LSP) server.
 *
 * This class handles the lifecycle of an LSP server process, including starting and stopping it.
 * It provides methods for sending JSON-RPC 2.0 requests, notifications, and responses to the
 * server via its standard input. It also reads and parses messages from the server's
 * standard output, emitting signals for received responses, notifications, and errors.
 *
 * This class also includes Python LSP server (pylsp) management functionality for code completion
 * and other Python-specific features.
 */
class LspClient : public QObject
{
Q_OBJECT

public:
  /**
   * @brief Constructs an LspClient object.
   * @param parent The parent QObject.
   */
  explicit LspClient(QObject *parent = nullptr);

  /**
   * @brief Destructor for the LspClient.
   */
  ~LspClient();

  /**
   * @brief Starts the LSP server process.
   * @param command The command to execute the LSP server (e.g., "pylsp").
   * @param arguments A list of arguments for the command.
   * @param workingDirectory The working directory for the process.
   */
  void startServer(const QString &command, const QStringList &arguments = QStringList(), const QString &workingDirectory = "");

  /**
   * @brief Stops the LSP server process.
   */
  void stopServer();

  /**
   * @brief Sends a JSON-RPC request to the LSP server.
   * @param method The method name of the request.
   * @param params The parameters for the request.
   * @return The ID of the sent message.
   */
  int sendRequest(const QString &method, const QJsonObject &params);

  /**
   * @brief Sends a JSON-RPC notification to the LSP server.
   * @param method The method name of the notification.
   * @param params The parameters for the notification.
   */
  void sendNotification(const QString &method, const QJsonObject &params);

  /**
   * @brief Sends a typed LSP message to the server.
   * @param message The LSP message to send.
   * @return The ID of the sent message (for requests), or empty QVariant for notifications.
   */
  QVariant sendMessage(std::unique_ptr<Lsp::LspMessage> message);

  /**
   * @brief Initialize the LSP server with client capabilities.
   * @param params The initialization parameters.
   * @return The ID of the initialize request.
   */
  int initialize(const Lsp::InitializeParams& params);

  /**
   * @brief Send initialized notification after receiving initialize response.
   */
  void sendInitialized();

  /**
   * @brief Shutdown the LSP server gracefully.
   * @return The ID of the shutdown request.
   */
  int shutdown();

  /**
   * @brief Send exit notification to terminate the server.
   */
  void sendExit();

  /**
   * @brief Open a text document.
   * @param textDocument The text document to open.
   */
  void didOpenTextDocument(const Lsp::TextDocumentItem& textDocument);

  /**
   * @brief Notify server of text document changes.
   * @param textDocument The versioned text document identifier.
   * @param contentChanges The list of content changes.
   */
  void didChangeTextDocument(const Lsp::VersionedTextDocumentIdentifier& textDocument,
                            const QList<Lsp::TextDocumentContentChangeEvent>& contentChanges);

  /**
   * @brief Notify server that a text document was saved.
   * @param textDocument The text document identifier.
   * @param text Optional text content (if includeText capability is enabled).
   */
  void didSaveTextDocument(const Lsp::TextDocumentIdentifier& textDocument,
                          const std::optional<QString>& text = std::nullopt);

  /**
   * @brief Notify server that a text document was closed.
   * @param textDocument The text document identifier.
   */
  void didCloseTextDocument(const Lsp::TextDocumentIdentifier& textDocument);

  /**
   * @brief Request completion items at a specific position.
   * @param textDocument The text document identifier.
   * @param position The position in the document.
   * @return The ID of the completion request.
   */
  int requestCompletion(const Lsp::TextDocumentIdentifier& textDocument, const Lsp::Position& position);

  /**
   * @brief Request to resolve additional information for a completion item.
   * @param item The completion item to resolve.
   * @return The ID of the completion item resolve request.
   */
  int requestCompletionItemResolve(const Lsp::CompletionItem& item);

  // Python LSP Manager functionality
  /**
   * @brief Initialize the Python LSP server
   * @param workspaceRoot The root directory of the workspace
   * @return True if initialization started successfully
   */
  bool initializePythonLsp(const QString& workspaceRoot);

  /**
   * @brief Shutdown the Python LSP server
   */
  void shutdownPythonLsp();

  /**
   * @brief Check if the Python LSP server is running and initialized
   * @return True if ready to handle requests
   */
  bool isPythonLspReady() const;

  /**
   * @brief Open a Python document with the LSP server
   * @param filePath The file path
   * @param content The file content
   */
  void openPythonDocument(const QString& filePath, const QString& content);

  /**
   * @brief Update a Python document with new content
   * @param filePath The file path
   * @param content The new file content
   * @param version The document version (optional, auto-incremented if <= 0)
   */
  void updatePythonDocument(const QString& filePath, const QString& content, int version = -1);

  /**
   * @brief Close a Python document
   * @param filePath The file path
   */
  void closePythonDocument(const QString& filePath);

  /**
   * @brief Request completion for Python code
   * @param filePath The file path
   * @param line The line number (0-based)
   * @param character The character position (0-based)
   * @return The request ID, or -1 if not ready
   */
  int requestPythonCompletion(const QString& filePath, int line, int character);

  /**
   * @brief Request to resolve additional information for a Python completion item
   * @param item The completion item to resolve
   * @return The request ID, or -1 if not ready
   */
  int requestPythonCompletionItemResolve(const Lsp::CompletionItem& item);

Q_SIGNALS:
  /**
   * @brief Emitted when a response from the server is received.
   * @param id The ID of the original request.
   * @param result The result object from the server's response.
   */
  void responseReceived(int id, const QJsonObject &result);

  /**
   * @brief Emitted when a notification from the server is received.
   * @param method The method of the notification.
   * @param params The parameters of the notification.
   */
  void notificationReceived(const QString &method, const QJsonObject &params);

  /**
   * @brief Emitted when an error response from the server is received.
   * @param id The ID of the original request.
   * @param error The error object from the server's response.
   */
  void errorReceived(int id, const QJsonObject &error);

  /**
   * @brief Emitted when the server process starts successfully.
   */
  void serverStarted();

  /**
   * @brief Emitted when the server process fails to start or crashes.
   * @param exitCode The exit code of the process.
   * @param exitStatus The exit status of the process.
   */
  void serverError(int exitCode, QProcess::ExitStatus exitStatus);

  /**
   * @brief Emitted when a typed LSP message is received.
   * @param message The received LSP message (caller takes ownership).
   */
  void messageReceived(Lsp::LspMessage* message);

  /**
   * @brief Emitted when the server is initialized.
   * @param result The initialization result.
   */
  void initialized(const Lsp::InitializeResult& result);

  // Python LSP Manager signals
  /**
   * @brief Emitted when the Python LSP server is ready
   */
  void pythonLspReady();

  /**
   * @brief Emitted when Python completion items are received
   * @param requestId The request ID
   * @param items The completion items
   */
  void pythonCompletionReceived(int requestId, const QList<Lsp::CompletionItem>& items);

  /**
   * @brief Emitted when a Python completion item resolve response is received
   * @param requestId The request ID
   * @param resolvedItem The resolved completion item with additional information
   */
  void pythonCompletionItemResolved(int requestId, const Lsp::CompletionItem& resolvedItem);

  /**
   * @brief Emitted when a Python LSP error occurs
   * @param message The error message
   */
  void pythonLspError(const QString& message);

  /**
   * @brief Emitted when a text document is opened.
   * @param params The did open parameters.
   */
  void textDocumentOpened(const Lsp::DidOpenNotification::DidOpenTextDocumentParams& params);

  /**
   * @brief Emitted when a text document is changed.
   * @param params The did change parameters.
   */
  void textDocumentChanged(const Lsp::DidChangeNotification::DidChangeTextDocumentParams& params);

  /**
   * @brief Emitted when a text document is saved.
   * @param params The did save parameters.
   */
  void textDocumentSaved(const Lsp::DidSaveNotification::DidSaveTextDocumentParams& params);

  /**
   * @brief Emitted when a text document is closed.
   * @param params The did close parameters.
   */
  void textDocumentClosed(const Lsp::DidCloseNotification::DidCloseTextDocumentParams& params);

  /**
   * @brief Emitted when completion items are received.
   * @param requestId The ID of the original completion request.
   * @param completionList The completion list received from the server.
   */
  void completionReceived(int requestId, const Lsp::CompletionList& completionList);

  /**
   * @brief Emitted when a completion item resolve response is received.
   * @param requestId The ID of the original completion item resolve request.
   * @param resolvedItem The resolved completion item with additional information.
   */
  void completionItemResolved(int requestId, const Lsp::CompletionItem& resolvedItem);


private slots:
  /**
   * @brief Slot to handle data received from the server's standard output.
   */
  void onReadyReadStandardOutput();

  /**
   * @brief Slot to handle data received from the server's standard error.
   */
  void onReadyReadStandardError();

  /**
   * @brief Slot to handle the server process finishing.
   * @param exitCode The exit code of the process.
   * @param exitStatus The exit status of the process.
   */
  void onProcessFinished(int exitCode, QProcess::ExitStatus exitStatus);

  // Python LSP Manager slots
  void onPythonLspServerStarted();
  void onPythonLspInitialized(const Lsp::InitializeResult& result);
  void onPythonLspCompletionReceived(int requestId, const Lsp::CompletionList& completionList);
  void onPythonLspCompletionItemResolved(int requestId, const Lsp::CompletionItem& resolvedItem);
  void onPythonLspError(int exitCode, QProcess::ExitStatus exitStatus);
  void onPythonLspInitializationTimeout();

private:
  /**
   * @brief Sends a message to the LSP server.
   * @param message The JSON object representing the message.
   */
  void sendMessage(const QJsonObject &message);

  /**
   * @brief Parses incoming data from the LSP server.
   * @param data The raw data received from the server process.
   */
  void parseResponse(const QByteArray &data);

  /**
   * @brief Process a received LSP message.
   * @param message The received message.
   */
  void processMessage(std::unique_ptr<Lsp::LspMessage> message);

  // Python LSP Manager private methods
  void startPythonLspServer();
  QString filePathToUri(const QString& filePath) const;
  QString uriToFilePath(const QString& uri) const;
  QStringList extractCompletionItems(const Lsp::CompletionList& completionList) const;

  QProcess *m_lspProcess;
  int m_messageId;
  QByteArray m_buffer;
  Lsp::LspMessageFactory m_messageFactory;
  Lsp::LspMessageRegistry m_messageRegistry;

  // Python LSP Manager member variables
  QString m_pythonWorkspaceRoot;
  bool m_pythonLspInitialized;
  bool m_pythonLspReady;
  QTimer* m_pythonInitializationTimer;
  mutable QMutex m_pythonLspMutex;
  QMap<QString, int> m_pythonDocumentVersions; // Track document versions
  int m_pythonNextVersion;
};

#endif // LSP_CLIENT_LSPCLIENT_H
